# 自动信息收集优化方向讨论

## 第一个问题：研究计划结构化对比机制

### 现状分析

你提到的很对，当前反思prompt的输入确实包含了原始研究信息：
- `research_topic`: 研究主题
- `research_plan`: 研究计划

但问题在于，虽然这些信息被传递给了AI，**prompt并没有明确指导AI如何系统性地将当前研究结果与原始计划进行对比**。

### 当前Prompt的局限性

查看现有的反思prompt (`autoResearchReflectionPrompt`)：

```
**Research Topic:** {research_topic}
**Original Research Plan:** {research_plan}
```

这只是简单地提供了研究计划作为背景信息，但缺少：

1. **明确的对比指令**：没有要求AI逐一检查计划中的关键要点
2. **结构化分析框架**：没有提供系统性的对比方法
3. **覆盖度量化要求**：没有要求AI量化每个计划要点的完成度

### 问题的真正症结

实际问题不是"没有提供研究计划信息"，而是：

**AI被要求做一个开放式的质量分析，而不是结构化的计划覆盖度检查**

当前的prompt更像是："基于这个计划背景，你觉得当前研究怎么样？"
而不是："按照这个计划的要点逐一检查，哪些已完成，哪些还缺失？"

### 改进思路

我们需要在prompt中加入**结构化对比的明确指令**，例如：

```
## 研究计划覆盖度分析
请将当前轮次的研究结果与原始研究计划进行逐项对比：

1. **提取研究计划关键要点**：
   - 从原始研究计划中识别3-5个核心研究方向/问题
   - 每个要点应该是具体、可验证的研究目标

2. **逐项覆盖度分析**：
   对每个关键要点评估：
   - ✅ 已充分覆盖 (有具体发现和数据)
   - 🔄 部分覆盖 (有初步信息但不够深入)  
   - ❌ 未涉及 (当前研究未触及此要点)

3. **结构化输出要求**：
   在 knowledge_completeness_score 计算中，必须基于上述覆盖度分析
```

### 重新审视：设计合理性分析

经过进一步思考，我意识到之前的分析可能有偏差。让我重新分析当前的设计合理性：

#### 当前设计的合理之处

1. **职责分工清晰**：
   - **反思(Reflection)**：专注当前轮次内容质量分析，局部视角
   - **评估(Evaluation)**：基于反思结果做全局战略决策，整体视角

2. **信息传递机制**：
   - `previous_recommendation`：评估给出的全局性建议，包含整体覆盖度信息
   - 这个设计实际上已经考虑了全局信息的传递

3. **避免重复工作**：
   - 研究计划本身就是核心要点，不需要每轮重新提取
   - 反思专注于"相对上轮的改进程度"，符合迭代式研究的特点

#### 重新思考真正的问题

可能真正的问题不是"缺乏结构化对比"，而是：

1. **评估阶段没有充分利用研究计划**：
   - 评估prompt中确实有原始研究计划
   - 但可能需要更明确地指导AI如何基于计划进行全局覆盖度分析

2. **反思和评估之间的信息传递可能不够精确**：
   - 反思给出的知识缺口是否准确传递给了评估？
   - 评估是否充分考虑了反思中的发现？

#### 基于代码的具体分析

通过查看代码，我发现了一些具体的逻辑问题：

##### 1. 评估阶段的信息传递问题

**发现的问题**：
```typescript
// 在performEvaluation中
const allRoundsDiscoveries = tasks
  .filter(task => task.taskType === 'reflection')
  .map(task => `第${task.roundNumber}轮: ${task.learning}`)
  .join('\n');
```

这里`allRoundsDiscoveries`只包含反思任务的`learning`内容，而不是实际的搜索发现。这意味着评估阶段看到的是"反思的结果"，而不是"具体的研究发现内容"。

##### 2. 评估prompt中的覆盖度计算指导

**当前prompt说**：
```
- Evaluate how well all SERP queries and discoveries cover the original research plan
```

**但实际输入是**：
- `all_query_keywords`：只是查询关键词的字符串拼接
- `all_rounds_discoveries`：反思任务的learning内容，不是实际研究内容

##### 3. 反思阶段的knowledge_completeness_score含义不明确

**反思prompt说**：
```
- **knowledge_completeness_score**: Rate how well current round addresses the research objectives (0-1)
```

但这是"当前轮次"相对于"研究目标"的完整性，还是相对于"当前已知知识缺口"的完整性？定义不够清晰。

##### 4. 真正的逻辑缺陷

1. **评估阶段缺乏具体研究内容**：评估决策基于的是反思摘要，而不是详细的研究发现
2. **覆盖度评估缺乏结构化对比**：虽然有research_plan，但prompt没有明确要求逐项检查计划要点
3. **两个completeness score的混淆**：反思的knowledge_completeness_score和评估的coverage_completeness_score职责不清

你认为这些分析对吗？哪个问题最关键？